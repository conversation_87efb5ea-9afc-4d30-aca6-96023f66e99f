using System.IO;
using Unity.VisualScripting;
using UnityEngine;


public static class FileTool
{
    /// <summary>
    /// 删除某文件夹下的所有文件
    /// </summary>
    /// <param name="path"></param> /// <summary>
    /// 删除某文件夹下的所有文件
    /// </summary>
    /// <param name="path"></param>
    public static void DeleteAllFilesInFolder(string path)
    {
        if (Directory.Exists(path))
        {
            // 获取文件夹下的所有文件
            string[] files = Directory.GetFiles(path);

            // 删除每个文件
            foreach (string file in files)
            {
                File.Delete(file);
                Debug.Log($"Deleted file: {file}");
            }

            string[] directories = Directory.GetDirectories(path);
            foreach (string directory in directories)
            {
                DeleteAllFilesInFolder(directory);
                Directory.Delete(directory);
                Debug.Log($"Clear directory: {directory}");
            }
        }
        else
        {
            Debug.LogError($"Folder does not exist: {path}");
        }
    }

    public static void CopyDirectory(string sourceDir, string destinationDir, string replaceOldText = "", string replaceNewText = "")
    {

        // 如果目标目录不存在，则创建
        if (!Directory.Exists(destinationDir) && !File.Exists(destinationDir))
        {
            Directory.CreateDirectory(destinationDir);
        }

        // 复制所有文件
        foreach (string file in Directory.GetFiles(sourceDir))
        {
            string suffix = file.Substring(sourceDir.Length);
            if (replaceOldText.Length > 0)
            {
                suffix = suffix.Replace(replaceOldText, replaceNewText);
            }
            string destFile = Path.Combine(destinationDir, suffix);
            File.Copy(file, destFile, true); // true 表示覆盖已存在的文件
        }

        // 递归复制子目录
        foreach (string subDir in Directory.GetDirectories(sourceDir))
        {
            string suffix = subDir.Substring(sourceDir.Length);

            if (replaceOldText.Length > 0)
            {
                suffix = suffix.Replace(replaceOldText, replaceNewText);
            }
            string destSubDir = Path.Combine(destinationDir, suffix);
            CopyDirectory(subDir, destSubDir); // 递归调用
        }
    }



}