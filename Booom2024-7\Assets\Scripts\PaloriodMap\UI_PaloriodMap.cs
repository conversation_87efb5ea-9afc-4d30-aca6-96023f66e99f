using System.Collections.Generic;
using System.Linq;
using cfg;
using DataCenter;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using static CursorManager;
public class PaloriodLine : LineGenerated
{
    private static Vector2 PaloriodSize = MapScreenAdpatation.InstaxEmpty * 0.6f;
    private static Vector2 TempSizeDelta = new();
    private static Vector3 TempPosition = new();
    private static Vector3 TempEulerAngles = new();

    public override void OnHide()
    {

    }

    public override void OnShow(params dynamic[] values)
    {

    }

    public void SetView(Vector3 parentPos, Vector3 childPos)
    {
        float x = (parentPos.x + childPos.x) / 2;
        float y1 = parentPos.y - PaloriodSize.y / 2;
        float y2 = childPos.y + PaloriodSize.y / 2;
        float y = (y1 + y2) / 2;
        float dist = Mathf.Sqrt((y2 - y1) * (y2 - y1) + (parentPos.x - childPos.x) * (parentPos.x - childPos.x));
        float rotZ;
        if (parentPos.x == childPos.x)
        {
            rotZ = 90;
        }
        else
        {
            float tan = (y2 - y1) / (childPos.x - parentPos.x);
            float radians = Mathf.Atan(tan);
            rotZ = radians * Mathf.Rad2Deg;
        }
        TempPosition.Set(x, y, 0);
        TempSizeDelta.Set(dist - 40, 7);
        TempEulerAngles.Set(0, 0, rotZ);
        gameObject.transform.localPosition = TempPosition;
        (gameObject.transform as RectTransform).sizeDelta = TempSizeDelta;
        (gameObject.transform as RectTransform).eulerAngles = TempEulerAngles;
    }

    public override void OnUIDestroy()
    {

    }
}


public class UI_PaloriodMap : PaloriodMapGenerated
{

    List<UI_PaloriodItem> _paloriodItems = new();
    List<PaloriodLine> _paloriodLines = new();
    EventButton _dragger;

    /// <summary>
    /// 两个拍立得之间的大小
    /// </summary>
    public int IntervalX = 300;

    /// <summary>
    /// 每排拍立得之间的高度差
    /// </summary>
    public int IntervalY = 450;

    public int MoveSpeed = 50;

    bool _isDrag = false;

    Vector3 _curPos = new();

    private float _scaleDelta = 0f;
    private float minX;
    private float maxX;
    private float minY;
    private float maxY;
    public float ScaleDelta
    {
        get
        {
            return _scaleDelta;
        }
        set
        {
            _scaleDelta = Mathf.Clamp(value, 0, 1.73f);
            tmp_UGUI_sliderScale.value = _scaleDelta;
        }
    }

    private float baseValue = 0.27f;
    private Vector3 _curAnchorScale = new Vector3();
    private EventButton _sliderButton;

    private Vector3 _tmpPos = new();

    /// <summary>
    /// 能看到的实际视口大小
    /// </summary>
    private Vector2 VisibleViewPort = new();

    /// <summary>
    /// 屏幕实际大小
    /// </summary>
    private Vector2 ScreenViewPort = new();

    private int[] unlockStoryIds;
    public override void Awake()
    {
        base.Awake();
        _dragger = new EventButton(node_dragDetection.gameObject);
        _dragger.OnPointerBeginDrag.AddListener((BaseEventData data) =>
        {
            _isDrag = true;
            // _curPos = Input.mousePosition;

            CursorManager.Ins().RefreshCursor(true);
        });

        _dragger.OnPointerEndDrag.AddListener((BaseEventData data) =>
        {
            _isDrag = false;

            CursorManager.Ins().RefreshCursor(false);
            // Move();
        });

        btn_btnClose.onClick.AddListener(() =>
        {
            UIManager.Ins().HideUI(this);
        });

        btn_btnLarger.onClick.AddListener(Larger);
        btn_btnShrink.onClick.AddListener(Shrink);

        tmp_UGUI_sliderScale.onValueChanged.AddListener(new UnityAction<float>(OnValueChanged));
        _sliderButton = new EventButton(tmp_UGUI_sliderScale);
        _sliderButton.OnPointerBeginDrag.AddListener((BaseEventData data) =>
        {
            AudioMgr.Ins().PlaySound("slider");
        });

        _sliderButton.OnPointerEndDrag.AddListener((BaseEventData data) =>
       {
           AudioMgr.Ins().PlaySound("slider");
       });

        Debug.LogError($"GetTransparentRect:{UIUtil.GetTransparentRect(img_frame.sprite.texture)}");
    }

    void InitMapParams()
    {
        Rect transRect = UIUtil.GetTransparentRect(img_frame.sprite.texture);
        img_frame.alphaHitTestMinimumThreshold = 0.1f; // 小于 0.1 透明的区域不响应点击
        VisibleViewPort = transRect.size;
        ScreenViewPort = img_frame.sprite.bounds.size * 100;
        Debug.LogError($"ScreenViewPort:{ScreenViewPort} GetTransparentRect:{transRect} ViewPort:{VisibleViewPort}");
    }


    void Larger()
    {
        ScaleDelta += 0.1f;
    }

    void Shrink()
    {
        ScaleDelta -= 0.1f;
    }

    void OnValueChanged(float value)
    {
        _scaleDelta = value;
        float newScaleVal = baseValue + value;
        _curAnchorScale = new(newScaleVal, newScaleVal, newScaleVal); ;
        node_MoveAnchor.localScale = _curAnchorScale;
        MarginCalculate();
    }

    void MarginCalculate()
    {
        float viewportWidth = VisibleViewPort.x; // 视口宽度（已知值）
        float viewportHeight = VisibleViewPort.y; // 视口高度（已知值）
        float imageWidth, imageHeight;
        Vector2 size = img_backGround.rectTransform.sizeDelta;
        imageWidth = size.x * _curAnchorScale.x;
        imageHeight = size.y * _curAnchorScale.y;

        // 计算边界
        maxX = -viewportWidth * 0.5f + imageWidth * 0.5f;
        minX = viewportWidth * 0.5f - imageWidth * 0.5f;
        maxY = -viewportHeight * 0.5f + imageHeight * 0.5f;
        minY = viewportHeight * 0.5f - imageHeight * 0.5f;
        ClampAnchorAndSetPosition(node_MoveAnchor.localPosition.x, node_MoveAnchor.localPosition.y);
    }

    bool canUpdate = false;

    void Update()
    {
        if (!canUpdate)
        {
            return;
        }
        bool isMouseOnOpaquePixel = UIUtil.IsMouseOnOpaquePixel(img_frame, Input.mousePosition, 0.1f);

        if (CursorManager.Ins().MouseCursorState == CursorState.Normal && isMouseOnOpaquePixel)
        {
            CursorManager.Ins().SetCursorState(CursorState.Map);
        }
        else if (CursorManager.Ins().MouseCursorState == CursorState.Map && !isMouseOnOpaquePixel && !_isDrag)
        {
            CursorManager.Ins().SetCursorState(CursorState.Normal);
        }

        Draging();
    }

    void Draging()
    {
        if (_isDrag)
        {
            if (_curPos == null || _curPos == default)
            {
                _curPos = Input.mousePosition;
            }
            else
            {
                Moving();
            }
        }
        else
        {
            if (_curPos != default)
            {
                Moving();
            }
            _curPos = default;
        }
    }
    void Moving()
    {
        Vector3 pos = node_MoveAnchor.localPosition;
        ClampAnchorAndSetPosition(pos.x + Input.mousePosition.x - _curPos.x, pos.y + Input.mousePosition.y - _curPos.y);
        _curPos = Input.mousePosition;
    }

    void ClampAnchorAndSetPosition(float x, float y)
    {   // -( _curSize.x * percent/2 -(1920-1300)/2)
        float PosX = Mathf.Clamp(x, minX, maxX);
        float PosY = Mathf.Clamp(y, minY, maxY);
        _tmpPos.Set(PosX, PosY, 0);
        (node_MoveAnchor as RectTransform).localPosition = _tmpPos;
        // Debug.LogError($"_______________________node_MoveAnchor.position:{node_MoveAnchor.position} localPosition:{node_MoveAnchor.localPosition}");

        // Debug.LogError($"+++++++++++++++++++++++++++++++++++node_MoveAnchor.position:{node_MoveAnchor.position} localPosition:{node_MoveAnchor.localPosition}");

    }



    #region  初始化创建map
    public void Traversal()
    {
        int curLength = 0;
        StoryLineNode node = StoryTree.Ins().OriginNode;
        StoryLineNode[] nodes = StoryTree.Ins().Roots;
        foreach (StoryLineNode n in nodes)
        {
            n.Paloriod = null;
        }

        TravelTreeNode(node, ref curLength, 1);
        TraversalLine(node);
    }

    void TraversalLine(StoryLineNode node)
    {
        if (!node.IsLeaf)
        {
            foreach (StoryLineNode child in node.Children)
            {
                PaloriodLine line = UIManager.Ins().CreateUI<PaloriodLine>();
                UIManager.Ins().ShowUI(line);
                line.transform.parent = node_Line;

                line.SetView(node.Paloriod.transform.localPosition, child.Paloriod.transform.localPosition);

                _paloriodLines.Add(line);
                TraversalLine(child);
            }
        }
    }


    void TravelTreeNode(StoryLineNode node, ref int curLength, int level)
    {
        if (!node.IsLeaf)
        {
            for (int i = 0; i < node.Children.Count; i++)
            {
                TravelTreeNode(node.Children[i], ref curLength, level + 1);
            }
        }

        float posX = 0;
        float posY = 0;
        UI_PaloriodItem item = UIManager.Ins().CreateUI<UI_PaloriodItem>();
        _paloriodItems.Add(item);
        if (unlockStoryIds.Contains(node.NodeCfg.Id) || node.NodeCfg.Id == 0)
        {
            UIManager.Ins().ShowUI(item, node);
        }
        else
        {
            UIManager.Ins().ShowUI(item, null);
        }
        item.SetBtnEnable(true);
        item.transform.parent = node_Paloriod;
        (item.transform as RectTransform).anchorMax = MapScreenAdpatation.AnchorCenter;
        (item.transform as RectTransform).anchorMin = MapScreenAdpatation.AnchorCenter;
        (item.transform as RectTransform).offsetMax = Vector2.zero;
        (item.transform as RectTransform).offsetMin = Vector2.zero;
        item.transform.localScale = MapScreenAdpatation.PariodLocalScale;
        node.Paloriod = item;
        if (node == StoryTree.Ins().OriginNode)
        {
            Debug.LogError("__________________OriginNode");
        }
        if (node.IsLeaf)
        {
            posX = curLength;
            curLength += IntervalX;
            posY = (6 - level) * IntervalY;
            item.transform.localPosition = new Vector3(posX, posY, 0);
        }
        else
        {
            float child_PosX_1 = node.Children[0].Paloriod.transform.localPosition.x;
            float child_PosX_2 = node.Children[node.Children.Count - 1].Paloriod.transform.localPosition.x;
            posX = (child_PosX_1 + child_PosX_2) / 2;
            posY = node.Children[0].Paloriod.transform.localPosition.y + IntervalY;
            item.transform.localPosition = new Vector3(posX, posY, 0);
        }
    }

    void SetView()
    {
        SetDefault();
        _curAnchorScale = MapScreenAdpatation.DefaultAnchorScale;
        (node_MoveAnchor.transform as RectTransform).localScale = _curAnchorScale;
        (node_MoveAnchor as RectTransform).anchorMin = MapScreenAdpatation.AnchorCenter;
        (node_MoveAnchor as RectTransform).anchorMax = MapScreenAdpatation.AnchorCenter;
        node_MoveAnchor.localPosition = Vector3.zero;

        img_backGround.rectTransform.sizeDelta = VisibleViewPort / _curAnchorScale;
        (node_dragDetection.transform as RectTransform).sizeDelta = VisibleViewPort / _curAnchorScale;
        img_backGround.transform.localPosition = Vector2.zero;
        node_dragDetection.transform.localPosition = Vector2.zero;
        node_Paloriod.localPosition = (VisibleViewPort - ScreenViewPort) / _curAnchorScale;
        node_Line.localPosition = node_Paloriod.localPosition;
        Debug.LogError($"node_Line position:{node_Line.localPosition} bgSizeDelta:{img_backGround.rectTransform.sizeDelta}");
    }
    void SetDefault()
    {
        img_backGround.rectTransform.sizeDelta = ScreenViewPort;
        (node_dragDetection as RectTransform).sizeDelta = VisibleViewPort;
        Transform imgBGtrans = img_backGround.transform;
        SetDefaultItem(ref imgBGtrans);
        SetDefaultItem(ref node_dragDetection);
        SetDefaultItem(ref node_MoveAnchor);
        SetDefaultItem(ref node_Paloriod);
        SetDefaultItem(ref node_Line);
    }

    void SetDefaultItem(ref Transform trans)
    {
        trans.localScale = Vector3.one;
        trans.position = Vector3.zero;
    }
    #endregion

    public override void OnShow(params dynamic[] values)
    {
        CursorManager.Ins().SetCursorState(CursorState.Map);
        InitMapParams();
        unlockStoryIds = DataService.Ins().Get<GlobeCoreData>().GetUnlockStoryNodeIds();
        RestrictMove.Instance.RestrictAdaptive();
        UIManager.Ins().Hide<UI_MaskPanel>();
        UIManager.Ins().Hide<UI_Ring>();
        Traversal();
        SetView();
        canUpdate = true;
        ScaleDelta = 0f;
        MarginCalculate();
        AudioMgr.Ins().PlaySound("opentheguidebook");
    }

    public override void OnHide()
    {
        foreach (UI_PaloriodItem item in _paloriodItems)
        {
            UIManager.Ins().HideUI(item);
        }
        _paloriodItems.Clear();
        foreach (PaloriodLine item in _paloriodLines)
        {
            UIManager.Ins().HideUI(item);
        }
        _paloriodLines.Clear();
        SetDefault();   //还原
        canUpdate = false;
        UIManager.Ins().Show<UI_MaskPanel>();
        UIManager.Ins().Show<UI_Ring>();
        RestrictMove.Instance.UnRestrictAdaptive();
        CursorManager.Ins().SetCursorState(CursorState.Normal);
    }

    public override void OnUIDestroy()
    {

    }
}