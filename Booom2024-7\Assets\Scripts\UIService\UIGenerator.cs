#if UNITY_EDITOR
using UnityEditor;
using UnityEngine;
using System.IO;
using System.Text;
using System.Collections;

using System;
using UnityEngine.UI;
using TMPro;
using UnityEngine.EventSystems;
using UnityEngine.Events;
using Unity.EditorCoroutines.Editor;
using Unity.VisualScripting;

public class UIGenerator : MonoBehaviour
{
    private static readonly Type[] UITypes = new Type[] {
    typeof(Image),typeof(RawImage),typeof(Canvas),  typeof(Toggle),typeof(Button),
    typeof(Text), typeof(TextMeshPro),typeof(TextMeshProUGUI),
   typeof(Slider),typeof(Scrollbar),typeof(Dropdown),  typeof(ScrollRect), typeof(InputField),
   typeof(UnityEngine.UIElements.Image), typeof(UnityEngine.UIElements.Button),typeof(UnityEngine.UIElements.Toggle),
        typeof(UnityEngine.UIElements.ScrollView),typeof(UnityEngine.UIElements.Slider),typeof(Animator)};
    private static readonly string[] UITypesAlias = new string[]
    {
        "img","rawImg","canvas","toggle","btn","text","tmp","tm","tmp_UGUI","slider","scrollbar","dropdown","scrollRect","inputField",
          "imgUIE","btnUIE","toggleUIE","scrollUIE","sliderUIE","animator"
    };

    // 标记一下是否是根节点
    private static readonly string RootTag = "RootTag";
    // UI预制体路径
    private static readonly string PrefabPath = "Assets/Resources/UIPrefabs";
    // 输出UI自动生成模版路径
    private static readonly string OutputPath = "Assets/Scripts/UIService/UIGeneratedScripts/";
    // UI模版
    private static readonly string TemplatePath = "Assets/Scripts/UIService/UITemplate.txt";
    private static readonly string TemplateDefinePath = "Assets/Scripts/UIService/TemplateDefinePath.txt";
    private static readonly string TemplateDefineOutputPath = "Assets/Scripts/UIService/";

    [ContextMenuItem("UI自动化生成", "GenerateUIScripts")]
    public string UIGenerate = "UI自动化生成";

    [MenuItem("Tools/游戏扩展/生成UI脚本", priority = 0)]
    public static void GenerateUIScripts()
    {
        DeleteAllFilesInFolder(OutputPath);
        EditorCoroutineUtility.StartCoroutineOwnerless(GenerateScriptsInternal());
    }

    /// <summary>
    /// 删除某文件夹下的所有文件
    /// </summary>
    /// <param name="path"></param>
    static void DeleteAllFilesInFolder(string path)
    {
        if (Directory.Exists(path))
        {
            // 获取文件夹下的所有文件
            string[] files = Directory.GetFiles(path);

            // 删除每个文件
            foreach (string file in files)
            {
                File.Delete(file);
                Debug.Log($"Deleted file: {file}");
            }

            string[] directories = Directory.GetDirectories(path);
            foreach (string directory in directories)
            {
                DeleteAllFilesInFolder(directory);
                Directory.Delete(directory);
                Debug.Log($"Clear directory: {directory}");
            }
        }
        else
        {
            Debug.LogError($"Folder does not exist: {path}");
        }
    }

    static StringBuilder _defineStringBuilder = new();
    static StringBuilder _definePathStringBuilder = new();

    static IEnumerator GenerateScriptsInternal()
    {

        yield return ProcessFolderAndSubfolders(PrefabPath);

    }

    static IEnumerator ProcessFolderAndSubfolders(string folderPath)
    {

        // 处理当前文件夹的预制体
        string[] prefabFiles = Directory.GetFiles(folderPath, "*.prefab");
        if (prefabFiles.Length > 0)
        {
            yield return GeneratePrefab(0, prefabFiles);
        }

        // 处理完当前文件夹后再处理子文件夹
        string[] subFolders = Directory.GetDirectories(folderPath);
        foreach (string subFolder in subFolders)
        {
            yield return ProcessFolderAndSubfolders(subFolder);
        }

        Resources.UnloadUnusedAssets();
        System.GC.Collect();
    }

    /// <summary>
    /// 生成预制体
    /// </summary>
    /// <param name="idx"></param>
    /// <param name="files"></param>
    /// <returns></returns>
    private static IEnumerator GeneratePrefab(int idx, string[] files)
    {
        if (idx < 0 || idx >= files.Length)
        {
            yield return null;

        }
        else if (Directory.Exists(files[idx]))
        {
            // 获取文件夹下的所有文件
            string[] childFiles = Directory.GetFiles(files[idx]);
            if (childFiles.Length > 0)
            {
                // 异步生成所有预制体，生成后添加文件
                EditorCoroutineUtility.StartCoroutineOwnerless(GeneratePrefab(0, childFiles));
            }
            yield return null;
        }
        else if (File.Exists(files[idx]))
        {
            string path = files[idx][17..];
            path = path.Replace('\\', '/');
            path = path.Split('.')[0];
            Debug.Log($"_____Prepare to generate prefab from path:{path}");

            ResourceRequest r = Resources.LoadAsync<GameObject>(path);
            yield return r;
            GameObject prefab = r.asset as GameObject;

            if (prefab != null)
            {

                GenerateCSharpFile(prefab, path);
                GenerateDefineFile();
            }
            else
            {
                Debug.LogError($"_____GenerateCSharpFile Fail,Can't instantiate prefab as GameObject,invalid path: {path}");
            }
            idx++;
            yield return EditorCoroutineUtility.StartCoroutineOwnerless(GeneratePrefab(idx, files));
        }
        else
        {
            Debug.LogError($"path {files[idx]} neither a file nor a directory");
        }
    }

    //真正的协同程序函数 用于开启异步加载资源
    private static IEnumerator ReallyLoadAsync(string path, UnityAction<GameObject> callback)
    {
        ResourceRequest r = Resources.LoadAsync<GameObject>(path);
        yield return r;
        callback(r.asset as GameObject);
    }

    /// <summary>
    /// 生成C#脚本
    /// </summary>
    /// <param name="prefab"></param>
    static void GenerateCSharpFile(GameObject prefab, string path)
    {
        string className = prefab.name + "Generated";
        string scriptContent = GenerateClassContent(prefab, className);

        // 确保输出路径存在
        if (!Directory.Exists(OutputPath))
        {
            Directory.CreateDirectory(OutputPath);
        }
        string pathPrefix = path.Substring(10, path.Length - 10 - prefab.name.Length);
        string realOutputPath = OutputPath + pathPrefix;
        if (!Directory.Exists(realOutputPath))
        {
            Directory.CreateDirectory(realOutputPath);
        }
        // 写入文件
        string filePath = Path.Combine(realOutputPath, className + ".cs");
        _defineStringBuilder.AppendLine($"\"{prefab.name}\",");
        _definePathStringBuilder.AppendLine($"\"{path}\",");
        try
        {
            File.WriteAllText(filePath, scriptContent);
        }
        catch (Exception e)
        {
            Debug.LogError($"_____GenerateCSharpFile Fail Error:{e},Can't write file to path: {filePath}");
        }
        finally
        {
            if (File.Exists(filePath))
            {
                Debug.Log($"_____GenerateCSharpFile Success,write file to path: {filePath}");
            }
            else
            {
                File.WriteAllText(filePath, scriptContent);
                Debug.Log($"_____GenerateCSharpFile Fail,and try again");
            }
        }
    }

    /// <summary>
    /// 生成C#脚本
    /// </summary>
    /// <param name="prefab"></param>
    static void GenerateDefineFile()
    {
        // 写入文件
        string filePath = Path.Combine(TemplateDefineOutputPath, "UIDefine.cs");
        string templateContent = File.ReadAllText(TemplateDefinePath);
        string scriptContent = templateContent.Replace("{UIPrefabName}", _defineStringBuilder.ToString());
        string pathContent = scriptContent.Replace("{UIPrefabPath}", _definePathStringBuilder.ToString());
        File.WriteAllText(filePath, pathContent);
    }

    static string GenerateClassContent(GameObject prefab, string className)
    {
        // 读取模板文件
        if (!File.Exists(TemplatePath))
        {
            Debug.LogError($"TemplatePath doesn't exsit: {TemplatePath}");
            return null;
        }

        string templateContent = File.ReadAllText(TemplatePath);

        // 生成节点变量和初始化代码
        StringBuilder[] nodeStringBuilders = new StringBuilder[4];
        for (int i = 0; i < nodeStringBuilders.Length; i++)
        {
            nodeStringBuilders[i] = new StringBuilder();
        }
        StringBuilder nodeVariables = nodeStringBuilders[0];
        StringBuilder nodeInitializations = nodeStringBuilders[1];
        StringBuilder nodeBtnOnclick = nodeStringBuilders[2];
        StringBuilder nodeUILanguage = nodeStringBuilders[3];

        TraversePrefab(prefab, prefab.transform, nodeStringBuilders);

        // 替换占位符
        string scriptContent = templateContent
            .Replace("{ClassName}", className)
            .Replace("{NodeVariables}", nodeVariables.ToString())
            .Replace("{NodeInitializations}", nodeInitializations.ToString())
             .Replace("{NodeBtnOnclick}", nodeBtnOnclick.ToString())
             .Replace("{NodeUILanguage}", nodeUILanguage.ToString());

        return scriptContent;
    }

    static void TraversePrefab(GameObject prefab, Transform node, StringBuilder[] nodeStringBuilders, string parentPath = "")
    {
        StringBuilder nodeVariables = nodeStringBuilders[0];
        StringBuilder nodeInitializations = nodeStringBuilders[1];
        StringBuilder nodeUILanguage = nodeStringBuilders[3];
        string nodePath;
        if (string.IsNullOrEmpty(parentPath))
        {
            nodePath = RootTag;
        }
        else if (parentPath == RootTag)
        {
            nodePath = $"{node.name}";
        }
        else
        {
            nodePath = $"{parentPath}/{node.name}";
        }

        string nodeName = node.name[0..];

        //是否能导出,只有大写固定的blockText才有多语言，非大写默认不绑定多语言,因为大写固定blockText不导出
        bool canExport = nodeName[0] >= 'a' && nodeName[0] <= 'z';
        bool isStartWithLan = node.name.StartsWith("Lan");
        bool isStartWithNode = node.name.StartsWith("node");
        nodeName = node.name.Replace(' ', '_');
        nodeName = nodeName.Replace('(', '_');
        nodeName = nodeName.Replace(')', '_');

        for (int i = 0; i < UITypes.Length; i++)
        {
            Type type = UITypes[i];
            Component script = node.gameObject.GetComponent(type.ToString());
            if (script != null)
            {
                string alias = UITypesAlias[i];
                if (canExport)
                {
                    GenerateNodeScripts(nodeStringBuilders, script, type, nodeName, nodePath, alias);
                }
                else if (isStartWithLan)
                {
                    //用于不导出的UI节点文字需要做多语言的block做多语言
                    GenerateUIText(script as UIBehaviour, nodeUILanguage, type, nodePath);
                }
            }
        }

        //若名字最开头有node会导出Transfrom类型
        if (isStartWithNode)
        {
            // 生成节点变量
            nodeVariables.AppendLine($"    protected Transform {nodeName};");
            if (nodePath == "ThisIsRoot")
            {
                nodeInitializations.AppendLine($"        {nodeName} = transform;");
            }
            else
            {
                // 生成初始化代码
                nodeInitializations.AppendLine($"        {nodeName} = transform.Find(\"{nodePath}\");");
            }

        }

        // 递归遍历子节点
        foreach (Transform child in node)
        {
            TraversePrefab(prefab, child, nodeStringBuilders, nodePath);
        }
    }

    static void GenerateNodeScripts(StringBuilder[] nodeStringBuilders, Component script, Type type, string nodeName, string nodePath, string alias = "")
    {
        StringBuilder nodeVariables = nodeStringBuilders[0];
        StringBuilder nodeInitializations = nodeStringBuilders[1];
        StringBuilder nodeBtnOnclick = nodeStringBuilders[2];
        if (alias.Length > 0)
        {
            nodeName = $"{alias}_{nodeName}";
        }
        // 生成节点变量
        nodeVariables.AppendLine($"    protected {type} {nodeName};");
        if (nodePath == RootTag)
        {
            nodeInitializations.AppendLine($"        {nodeName} = transform.gameObject.GetComponent<{type}>();");
        }
        else
        {
            // 生成初始化代码
            nodeInitializations.AppendLine($"        {nodeName} = transform.Find(\"{nodePath}\").gameObject.GetComponent<{type}>();");
        }

        if (script is Button)
        {

            nodeBtnOnclick.AppendLine($"        {nodeName}.onClick.AddListener(() =>");
            nodeBtnOnclick.AppendLine("        {");
            nodeBtnOnclick.AppendLine($"            EventDispatcher.GameEvent.DispatchEvent({"\"UIBtnOnClick\""}, {nodeName}.name , GetType().ToString());");
            nodeBtnOnclick.AppendLine($"            Debug.Log($\"_______UIBtnOnClick,name:{nodeName} typeName:{{GetType()}}\");");
            nodeBtnOnclick.AppendLine("        });");
        }

    }

    static void GenerateUIText(UIBehaviour script, StringBuilder nodeUILanguage, Type type, string nodePath)
    {
        if (script is Text || script is TextMeshPro || script is TextMeshProUGUI)
        {
            if (nodePath == RootTag)    //说明node在顶层
            {
                nodeUILanguage.AppendLine($"        InitLanguage(transform.gameObject.GetComponent<{type}>());");
            }
            else
            {
                nodeUILanguage.AppendLine($"        InitLanguage(transform.Find(\"{nodePath}\").gameObject.GetComponent<{type}>());");
            }

        }
    }

}
#endif