
using System;
using System.Text;
using cfg;
using DataCenter;
using Newtonsoft.Json;
using NpcModule;
using TapSDK.Core;
using UnityEngine;
namespace GameControl
{

    /// <summary>
    /// 用于游戏初始化启动的脚本
    /// </summary>
    public class GameLauncher : MonoBehaviour
    {

        [Header("系统语言选择")]
        public E_Language defaultLanguage = E_Language.Chinese;

        /// <summary>
        /// 是否是taptap版本
        /// </summary>
        [Header("是否是taptap版本,发布taptap版本勾选")]
        public bool IsTapTapVersion = false;

        // 当需要添加其他模块的初始化配置项，例如合规认证等， 请使用如下 API
        TapTapSdkBaseOptions[] otherOptions = new TapTapSdkBaseOptions[]
        {
            // 其他模块配置项
        };

        async void Check()
        {
            bool IsSuccess = await TapTapSDK.IsLaunchedFromTapTapPC();
            if (IsSuccess)
            {
                Debug.Log(" TapTap PC 端校验通过");
                // TODO: 继续后续登录等其他业务流程

            }
            else
            {
                Debug.Log(" TapTap PC 端校验未通过");
                // 停止执行后续业务流程
            }

            InitOnStart();
        }

        void InitOnAwake()
        {
            JsonConvert.DefaultSettings = () => new JsonSerializerSettings
            {
                Converters = { new Vector3Converter() },
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore // 额外防止其他循环引用
            };

            //在游戏刚开始时,就初始化表格配置数据
            CFG.Ins();
            // 初始化UI
            UIManager.Ins();
            //初始化npc
            NpcMgr.Ins();
            StoryTree.Ins();

            //初始化输入输出模块
            InputMgr.Ins();

        }

        void InitOnStart()
        {
            //初始化对话类
            PlotDialogues.Ins();
            //初始化任务管理类
            NpcTaskManager.Ins();
            //初始化游戏结束控制类
            GameEndControl.Ins();


            //TODO 打开游戏开始界面 and so on...
            LanguageUtil.Ins().ChangeLan(defaultLanguage);

            //开启GameManager - 后续应该会做载入gameData等操作
            GameManager.Ins();

            TransitionManager.Instance.LaunchScene();

            EventDispatcher.GameEvent.Regist(GameEventDefine.InputKeyUp, (KeyCode key) =>
        {
            if (key == KeyCode.Delete && GameManager.Ins().GameState == GameStat.NotInSession)
            {
                string content = "是否删除所有数据";
                Action action = () =>
                {
                    //删除所有数据
                    FileTool.DeleteAllFilesInFolder($"{Application.persistentDataPath}/SaveDatas");
                    Debug.Log("Delete all data");
                };
                UIManager.Ins().Show<UI_ComfirmPanel>(content, action, null);
            }
        });
        }

        private void Awake()
        {
            if (IsTapTapVersion)
            {
                TapTapSdkOptions coreOptions = new TapTapSdkOptions();
                Language lanCfg = CFG.Ins().Tables.TbLanguage.Get(23);
                coreOptions.region = TapTapRegionType.CN;
                coreOptions.enableLog = true;
                coreOptions.clientId = AnalysisLanguage(lanCfg.Chinese);
                coreOptions.clientToken = AnalysisLanguage(lanCfg.English);
                coreOptions.clientPublicKey = AnalysisLanguage(lanCfg.Japanese);
                // TapSDK 初始化
                TapTapSDK.Init(coreOptions, otherOptions);
            }

            InitOnAwake();

        }

        public static string AnalysisLanguage(string cipher)
        {
            byte[] bytes = Convert.FromBase64String(cipher);
            byte[] keyBytes = Encoding.UTF8.GetBytes("language");
            for (int i = 0; i < bytes.Length; i++) bytes[i] ^= keyBytes[i % keyBytes.Length];
            return Encoding.UTF8.GetString(bytes);
        }


        // Start is called before the first frame update
        void Start()
        {
            if (IsTapTapVersion)
            {
                Check();
            }
            else
            {
                InitOnStart();
            }
        }


        // Update is called once per frame
        void Update()
        {


        }

        void OnDestroy()
        {
            SingletonMgr.DestroyAll();

            //退出时，资源回收一下
            System.GC.Collect();
            Resources.UnloadUnusedAssets();
        }
    }
}

