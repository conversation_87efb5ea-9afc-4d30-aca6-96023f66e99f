

using System;
using System.Collections.Generic;
using DataCenter;
using UnityEngine.Events;

public class GuideManager : SingletonLifeCircle<GuideManager>
{
    private int _curGuideId = -1;
    public int CurGuideId
    {
        get
        {
            return _curGuideId;
        }
    }

    /// <summary>
    /// 当前触发过但还没有完成的引导
    /// </summary>
    private Dictionary<int, GuideView> _guideViewCache = new();


    /// <summary>
    /// 引导结束时回调 - 统一
    /// </summary>
    private UnityAction<int> _guideOnCompleteAction;
    private GuideData _data;

    public void AddGuideOnCompleteAction(UnityAction<int> action)
    {
        if (_guideOnCompleteAction == null)
        {
            _guideOnCompleteAction = new(action);
        }
        else
        {
            _guideOnCompleteAction += action;
        }
    }

    public void RemoveGuideOnCompleteAction(UnityAction<int> action)
    {
        if (_guideOnCompleteAction != null)
        {
            _guideOnCompleteAction -= action;
        }
    }

    public override void Init()
    {
        _data = DataService.Ins().Get<GuideData>();
        AddGuideOnCompleteAction(OnGuideCompleteFinish);
    }

    private void OnGuideCompleteFinish(int id)
    {
        if (id >= 2 && id < 6)
        {
            _data.UnlockGuideId(++id);
        }
    }

    protected override void OnDestroy()
    {
        if (_guideOnCompleteAction != null)
        {
            _guideOnCompleteAction = null;
        }

        _guideViewCache.Clear();
        _guideViewCache = null;
    }

    /// <summary>
    /// 触发引导
    /// </summary>
    public void TriggerGuide(int id)
    {
        if (!CheckGuideIdValid(id))
        {
            return;
        }

        //说明当前引导还没有结束
        if (_curGuideId != -1)
        {
            return;
        }
        _curGuideId = id;
        if (!_guideViewCache.ContainsKey(id))
        {
            _guideViewCache.Add(id, UIManager.Ins().CreateUI<GuideView>());
        }
        UIManager.Ins().ShowUI(_guideViewCache[id], id);
    }

    public bool IsCurGuideViewShow()
    {
        if (_curGuideId != -1 && _guideViewCache.ContainsKey(_curGuideId))
        {
            return _guideViewCache[_curGuideId].IsShow;
        }
        return false;
    }

    public void StopCurGuide()
    {
        if (_guideViewCache.ContainsKey(_curGuideId))
        {
            UIManager.Ins().HideUI(_guideViewCache[_curGuideId]);
            _curGuideId = -1;
        }
    }

    /// <summary>
    /// 根据id停止引导，如果当前引导不是该id就无事发生
    /// </summary>
    /// <param name="id"></param>
    public void StopGuideById(int id)
    {
        if (_guideViewCache.ContainsKey(id))
        {
            UIManager.Ins().HideUI(_guideViewCache[id]);
            if (id == _curGuideId)
            {
                _curGuideId = -1;
            }
        }
    }


    /// <summary>
    /// 完成引导
    /// </summary>
    /// <param name="id">引导id</param>
    /// <param name="callback">引导完成回调</param>
    /// <returns></returns>
    public bool FinishGuide(int id, Action callback = null)
    {
        if (!CheckGuideIdValid(id))
        {
            return false;
        }
        _data.AddFinishedGuide(id);
        if (id == _curGuideId)
        {
            _curGuideId = -1;
        }
        _guideOnCompleteAction.Invoke(id);
        RemoveGuideCacheById(id);
        callback?.Invoke();
        return true;
    }

    public void StopAllGuide()
    {
        foreach (GuideView guideView in _guideViewCache.Values)
        {
            UIManager.Ins().HideUI(guideView);
        }
        _curGuideId = -1;
    }

    public bool CheckGuideIdValid(int id)
    {
        _data = DataService.Ins().Get<GuideData>();
        //如果已解锁id里没有triggerId，则返回
        if (!_data.IsGuideIdUnlock(id))
        {
            return false;
        }

        if (_data.IsGuideDone(id))
        {
            return false;
        }
        return true;
    }

    public void AddUnlockGuideId(int cfgId)
    {
        _data.UnlockGuideId(cfgId);
    }

    void RemoveGuideCacheById(int id)
    {
        if (_guideViewCache.ContainsKey(id))
        {
            if (_guideViewCache[id].IsShow)
            {
                UIManager.Ins().HideUI(_guideViewCache[id]);
            }
            _guideViewCache.Remove(id);
        }
    }
}