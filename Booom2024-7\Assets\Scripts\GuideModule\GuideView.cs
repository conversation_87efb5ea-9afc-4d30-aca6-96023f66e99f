
using cfg;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

enum HandDirect
{
    RightDown = 1,
    Left = 2,
    Right = 3,
    Up = 4,
    Down = 5,
    LeftDown = 6,
}

enum TextTypingStat
{
    Prepare,
    Texting,
    Done
}

public class GuideView : GuideViewGenerated
{
    Quaternion right = new Quaternion(0, 0, 0.707106829f, 0.707106829f);
    Quaternion left = new Quaternion(0, 0, -0.707106829f, 0.707106829f);
    float moveDistance = 3f; // 移动距离
    float duration = 0.5f;    // 持续时间
    Ease easeType = Ease.OutQuad; // 缓动类型
    float FontWidth = 35;

    private float Interval = 0.02f;
    private float coolDown = 0;
    private int index = 0;
    private string _curStr;

    private Animator _circleAnim;
    private Animator _circleAnim_2;
    private GuideInfo _guideInfo;

    private Tweener _handTweener;
    /// <summary>
    /// 当前持有的手爪子
    /// </summary>
    private UnityEngine.UI.Image m_HandImg;
    private Image[] _handImgs;

    private TextTypingStat _typingStat = TextTypingStat.Prepare;
    public void Init(int cfgId)
    {

        _guideInfo = CFG.Ins().Tables.TbGuideInfo.Get(cfgId);
        _handImgs = new Image[]
            {
            img_handRight,img_handLeft,img_handDown,img_handUp
            };
        switch (_guideInfo.HandDirect)
        {
            case (int)HandDirect.Left:
                SetImgHandActive(img_handLeft);
                m_HandImg.transform.rotation = left;
                break;
            case (int)HandDirect.Right:
                SetImgHandActive(img_handRight);
                m_HandImg.transform.rotation = right;
                break;
            case (int)HandDirect.LeftDown:
                SetImgHandActive(img_handLeft);
                m_HandImg.transform.rotation = Quaternion.identity;
                break;
            case (int)HandDirect.RightDown:
                SetImgHandActive(img_handRight);
                m_HandImg.transform.rotation = Quaternion.identity;
                break;
            case (int)HandDirect.Down:
                SetImgHandActive(img_handDown);
                break;
            case (int)HandDirect.Up:
                SetImgHandActive(img_handUp);
                break;
            default:
                break;
        }
        node_View.localPosition = CFG.Ins().ConvertVector3(_guideInfo.InfoPos);
        _typingStat = TextTypingStat.Prepare;
        float width = FontWidth * _guideInfo.GuideText.Length;
        tm_guideInfo.rectTransform.sizeDelta = new(width, tm_guideInfo.rectTransform.sizeDelta.y);
        _curStr = "";
        tm_guideInfo.text = "";

        btn_close.gameObject.SetActive(_guideInfo.Id == 6);
        btn_close.onClick.RemoveAllListeners();
        btn_close.onClick.AddListener(() =>
        {
            //完成当前引导
            GuideManager.Ins().FinishGuide(cfgId);
        });
    }

    void SetImgHandActive(Image showImg)
    {
        foreach (Image img in _handImgs)
        {
            img.gameObject.SetActive(img == showImg);
            if (img == showImg)
            {
                img.color = Color.clear;
                m_HandImg = img;
            }
        }
    }

    void Awake()
    {
        base.Awake();
        uiState = E_UIState.InSession;
        layer = E_UILayer.Top;
        _circleAnim = img_circle.gameObject.GetComponent<Animator>();
        _circleAnim_2 = img_circle_2.gameObject.GetComponent<Animator>();
        tm_guideInfo.text = "";

    }

    public override void OnShow(params dynamic[] values)
    {
        Init((int)values[0]);
        if (_guideInfo.Id == 1 || _guideInfo.Id == 7)
        {
            img_circle.rectTransform.sizeDelta = CFG.Ins().ConvertVector2(_guideInfo.CircleSize);
            img_circle.gameObject.transform.localPosition = CFG.Ins().ConvertVector3(_guideInfo.CirclePos);
        }
        else if (_guideInfo.Id == 2)
        {
            img_circle_2.rectTransform.sizeDelta = CFG.Ins().ConvertVector2(_guideInfo.CircleSize);
            img_circle_2.gameObject.transform.localPosition = CFG.Ins().ConvertVector3(_guideInfo.CirclePos);
        }
        else
        {
            TypeText();
        }
        img_circle.gameObject.SetActive(_guideInfo.Id == 1 || _guideInfo.Id == 7);
        img_circle_2.gameObject.SetActive(_guideInfo.Id == 2);
        EventDispatcher.GameEvent.Regist<Animator>(GameEventDefine.GuideCircleShowAnimEnd, OnCircleShowAnimEnd);
    }

    public override void OnHide()
    {
        ClearHandAnim();
        _typingStat = TextTypingStat.Done;

        EventDispatcher.GameEvent.UnRegist<Animator>(GameEventDefine.GuideCircleShowAnimEnd, OnCircleShowAnimEnd);
    }


    public override void OnUIDestroy()
    {

    }

    void OnCircleShowAnimEnd(Animator animator)
    {
        if (_circleAnim == animator)
        {
            TypeText();
        }
        if (_circleAnim_2 == animator)
        {
            TypeText();
        }
    }

    void TypeText()
    {
        _curStr = "";
        index = 0;
        _typingStat = TextTypingStat.Texting;
    }


    void Update()
    {
        if (_typingStat != TextTypingStat.Texting)
        {
            return;
        }

        if (coolDown < Interval)
        {
            coolDown += Time.deltaTime;
            return;
        }
        coolDown = 0;
        if (index < _guideInfo.GuideText.Length)
        {
            _curStr += _guideInfo.GuideText[index];
            tm_guideInfo.text = _curStr;
            index++;
            if (index == _guideInfo.GuideText.Length)
            {
                _typingStat = TextTypingStat.Done;
                ShowHand();
            }
        }
    }

    void ShowHand()
    {
        m_HandImg.color = Color.white;
        switch (_guideInfo.HandDirect)
        {
            case (int)HandDirect.Left:
            case (int)HandDirect.Right:
                // 左右移动（X轴）
                _handTweener = m_HandImg.rectTransform.DOMoveX(m_HandImg.rectTransform.position.x + moveDistance, duration)
                      .SetEase(easeType)
                      .SetLoops(-1, LoopType.Yoyo).SetDelay(duration);
                break;
            case (int)HandDirect.Up:
            case (int)HandDirect.Down:
            case (int)HandDirect.RightDown:
            case (int)HandDirect.LeftDown:
                // 上下移动（Y轴）
                _handTweener = m_HandImg.rectTransform.DOMoveY(m_HandImg.rectTransform.position.y + moveDistance, duration)
                      .SetEase(easeType)
                      .SetLoops(-1, LoopType.Yoyo)
                      .SetDelay(duration); // 延迟执行避免重叠
                break;
            default:
                break;
        }
    }

    void ClearHandAnim()
    {
        if (_handTweener != null)
        {
            _handTweener.Kill();
            _handTweener = null;
        }
        m_HandImg.color = Color.clear;
    }

}