

public class UI_PaloriodIntroPanel : PaloriodIntroPanelGenerated
{
    private UI_PaloriodItem _paloriod;
    public override void Awake()
    {
        base.Awake();
        btn_btnClose.onClick.AddListener(() =>
        {
            UIManager.Ins().HideUI(this);
        });

        _paloriod = UIManager.Ins().CreateUI<UI_PaloriodItem>();

    }

    public override void OnShow(params dynamic[] values)
    {
        AudioMgr.Ins().PlaySound("enterPhoto");
        _paloriod.transform.parent = node_Anchor.transform;
        _paloriod.SetPinActive(false);
    }

    public override void OnHide()
    {
        
    }

    public override void OnUIDestroy()
    {

    }
}