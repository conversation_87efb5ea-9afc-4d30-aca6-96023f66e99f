using System.Collections.Generic;
using UnityEngine;


/// <summary>
/// 1.Input类
/// 2.事件中心模块
/// 3.公共Mono模块的使用 
/// </summary>
public class InputMgr : SingletonLifeCircle<InputMgr>
{
    private bool _isLock;
    private Dictionary<KeyCode, int> _lockDic;

    public override void Init()
    {
        _lockDic = new();
        _isLock = false;
        MonoMgr.Ins().AddUpdateListener(OnUpdate);
    }

    protected override void OnDestroy()
    {
        _lockDic.Clear();
        _lockDic = null;
        MonoMgr.Ins().RemoveUpdateListener(OnUpdate);
    }


    private void OnUpdate()
    {
        if (_isLock)
        {
            return;
        }
        CheckKeyCode(KeyCode.Delete);
        CheckKeyCode(KeyCode.Escape);
        CheckKeyCode(KeyCode.Alpha0);
        CheckKeyCode(KeyCode.Alpha1);
        CheckKeyCode(KeyCode.Alpha2);
        CheckKeyCode(KeyCode.Alpha3);
    }

    /// <summary>
    /// 是否锁定所有key
    /// </summary>
    /// <param name="isLock"></param>
    public void IsLockAllKey(bool isLock)
    {
        _isLock = isLock;
    }

    /// <summary>
    /// 是否开启某个按键检测
    /// </summary>
    /// <param name="keyCode">按键检测</param>
    /// <param name="isLock">是否锁定</param>
    public void RegistLockCheck(KeyCode keyCode, bool isLock)
    {
        if (_lockDic.ContainsKey(keyCode))
        {
            if (isLock == true)
            {
                _lockDic[keyCode]++;
            }
            else
            {
                _lockDic[keyCode]--;
                if (_lockDic[keyCode] < 0)
                {
                    _lockDic[keyCode] = 0;
                }
            }
        }
        else
        {
            if (isLock == true)
            {
                _lockDic.Add(keyCode, 1);
            }
            else
            {
                _lockDic.Add(keyCode, 0);
            }

        }
    }

    private void CheckKeyCode(KeyCode keyCode)
    {
        if (Input.GetKeyDown(keyCode))
        {
            if (!_lockDic.ContainsKey(keyCode) || _lockDic[keyCode] <= 0)
            {
                //事件中心模块 分发按下抬起事件
                EventDispatcher.GameEvent.DispatchEvent(GameEventDefine.InputKeyDown, keyCode);
            }
        }

        if (Input.GetKeyUp(keyCode))
        {
            if (!_lockDic.ContainsKey(keyCode) || _lockDic[keyCode] <= 0)
            {
                //事件中心模块 分发按下抬起事件
                EventDispatcher.GameEvent.DispatchEvent(GameEventDefine.InputKeyUp, keyCode);
            }
        }
    }
}
