/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> ʢᵕᴗᵕʡ 
 * @Date: 2025-03-18 17:35:11 
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON> ʕᴥ•ʔ
 * @Last Modified time: 2025-03-20 14:13:35
 * @Description: 游戏管理 - 顶层
 */

using System;
using CompositeModule;
using DataCenter;
using ShelfModule;
using UnityEngine;

namespace GameControl
{

    /// <summary>
    /// 用于游戏管理
    /// </summary>
    public class GameManager : SingletonAutoMono<GameManager>
    {
        public GameObject SceneChange;
        private GameSession _gameSession;

        /// <summary>
        /// 当前进行的一局游戏
        /// </summary>
        public GameSession GameSession
        {
            get
            {
                return _gameSession;
            }
        }

        private GameStat _gameStat = GameStat.NotInSession;
        public GameStat GameState
        {
            get
            {
                return _gameStat;
            }
        }

        void Awake()
        {

        }

        // Start is called before the first frame update
        void Start()
        {
            //注册escape按键响应
            EventDispatcher.GameEvent.Regist(GameEventDefine.InputKeyUp, (KeyCode key) =>
                  {
                      if (key == KeyCode.Escape && _gameStat == GameStat.InSession && UIManager.Ins().IsOnlyInSessionUIOpen())
                      {
                          UI_PausingMenu pausingMenu = UIManager.Ins().GetUI<UI_PausingMenu>();
                          if (!pausingMenu.IsShow)
                          {
                              AudioMgr.Ins().PlaySound("escbottom");
                              UIManager.Ins().ShowUI(pausingMenu);
                          }
                      }
                  });
        }


        // Update is called once per frame
        void Update()
        {

        }

        public void GameStart()
        {
            TransitionManager.getInstance().LoadInsidePersistent(() =>
            {
                _gameStat = GameStat.InSession;
                DataService.Ins().SaveGlobeData();

                DataService.Ins().StartNewGameAndLoadData();

                ActsOnSceneChange.Instance.AddAll();
                //RestrictMove.Instance.AddMoveObjs();
                AssignStartingPerformData();
                GameBeginInternal();
                PickedItems.getInstance().AutoPick(true);
            });

        }

        /// <summary>
        /// 只用于假结局，所以不用重新加载局内持久场景
        /// </summary>
        public void GameRestart()
        {
            _gameStat = GameStat.InSession;

            DataService.Ins().LeaveSession();

            DataService.Ins().SaveGlobeData();

            DataService.Ins().StartNewGameAndLoadData();

            RingActs.Instance.OnLeaveSession();

            //RestrictMove.Instance.AddMoveObjs();
            AssignStartingPerformData();
            GameBeginInternal();
            PickedItems.getInstance().AutoPick(true);

            //防止翻车强制刷新一下
            ShelfMgr.Ins().LeaveScene();
            CompositeMgr.Ins().LeaveScene();
            BiomorphMgr.Ins().LeaveScene();

            ShelfMgr.Ins().EnterScene();
            CompositeMgr.Ins().EnterScene();
            BiomorphMgr.Ins().EnterScene();
        }

        public void GameContinue(int slotIdx)
        {
            TransitionManager.getInstance().LoadInsidePersistent(() =>
            {
                _gameStat = GameStat.InSession;

                DataService.Ins().SaveGlobeData();
                DataService.Ins().ContinueGameAndLoadData(slotIdx);

                ActsOnSceneChange.Instance.AddAll();
                GameBeginInternal();

                PickedItems.getInstance().AutoPick(false);
                //清除现有的npc任务
                NpcTaskManager.Ins().ResetAll();
                bool willComes2End = Try2AssignEnding();
                if (!willComes2End)
                {
                    bool bringsNpcTask = NpcTaskQueue.Ins().CheckNpcTask(false, false);
                    MissBoxPerformManager.Ins().CheckMissBoxMonologue(_gameSession.Data.StoryNodeId);
                }

                ItemGuide.Instance.ResetThis();
            });

        }

        void GameBeginInternal()
        {

            _gameSession = new GameSession();   //新开一局
            _gameSession.GameEnter();
            RestrictMove.Instance.AddMoveObjs();
            DialogueHistory.Ins().RefreshAll();
            // 激活局内持久场景
            SwitchStartingScene();
            AudioMgr.Ins().PlayBGM("ValleyBackgroundMusic2");
        }

        public void ExitSession()
        {
            //Debug.Log("ExitSession excuted");
            if (_gameSession != null)
            {
                _gameSession.GameExit();
                _gameSession = null;
            }
            _gameStat = GameStat.NotInSession;
            DataService.Ins().LeaveSession();

            //退出时，资源回收一下
            System.GC.Collect();
            Resources.UnloadUnusedAssets();

            TransitionManager.Instance.TransitionDelayingDark(StartSceneType.BeginScene);
            TransitionManager.getInstance().UnloadInsidePersistent(); // 卸载局内持久场景
        }

        public void GameExit()
        {
            DataService.Ins().SaveAllData();
            Application.Quit();
            // 编辑器专用退出
#if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
#endif
        }

        /// <summary>
        /// 使用药水 - 改变故事节点
        /// </summary>
        /// <param name="potionId"></param>
        public void UsePotion2ChangeStoryNode(int potionId)
        {
            GlobeCoreData data = DataService.Ins().Get<GlobeCoreData>();
            data.TotalDrintCnt++;

            NpcTaskDataInRealSession npcData = DataService.Ins().Get<NpcTaskDataInRealSession>();
            npcData.EverMetLanForThisTotalDrinkCnt = false;

            _gameSession.UsePotion2ChangeStoryNode(potionId);

        }

        public void AfterBiomorphChange()
        {
            _gameSession.AfterBiomorphChange();
        }

        #region 分发游戏开始时演出
        /// <summary>
        /// 只有真的开始新游戏时调用，根据上一局的情况，判断这一局开头会不会复活在indoor3以外的地方
        /// </summary>
        private void AssignStartingPerformData()
        {
            StartingPerformData data = DataService.Ins().Get<StartingPerformData>();
            Debug.Log("game mgr assign: startingdata hash code: " + data.GetHashCode());

            if (MissBoxPerformManager.Ins().WillRebirthWithMissBox())
            {
                data.WillRebirthWithMissBox = true;
            }
            if (NpcTaskQueue.Ins().WillMeetLanRightAfterRebirth())
            {
                data.WillMeetLanAfterRebirth = true;
                Debug.Log("will meet lan after rebirth: " + data.WillMeetLanAfterRebirth);
            }
            //data.WillRebirthWithMissBox = true;
            //data.WillMeetLanAfterRebirth = true;

            //DataService.Ins().Get<GlobeCoreData>().TotalDrintCnt = 2;
        }

        /// <summary>
        /// 开始和继续游戏都调用，判断是否复活在别的地方。因为可能玩家开局同时要进行盒子女士和诸葛兰演出，
        /// 但走完盒子女士对白以后，就退出了，下一局开始的时候就直接进诸葛兰表演
        /// </summary>
        private void SwitchStartingScene()
        {
            StartingPerformData data = DataService.Ins().Get<StartingPerformData>();
            //Debug.Log("game mgr switch: startingdata hash code: " + data.GetHashCode());

            if (data.WillRebirthWithMissBox)
            {
                Debug.Log("WILL REBIRTH WITH MISS BOX");
                //MissBoxPerformManager.Ins().StartWithMissBox();
                TransitionManager.Instance.SetBlack();
                if (data.WillMeetLanAfterRebirth)
                {
                    TransitionManager.Instance.Transition(StartSceneType.BloodScabValleyIndoor_4,
                        () => { MissBoxPerformManager.Ins().StartWithMissBox(); TransitionManager.Instance.AllBlack2Clear(); });
                }
                else
                {
                    TransitionManager.Instance.Transition(StartSceneType.BloodScabValleyIndoor_3,
                        () => { MissBoxPerformManager.Ins().StartWithMissBox(); TransitionManager.Instance.AllBlack2Clear(); });
                }
            }
            else if (data.WillMeetLanAfterRebirth)
            {
                Debug.Log("WILL REBIRTH WITH MISS LAN");
                TransitionManager.Instance.Transition(StartSceneType.BloodScabValleyIndoor_4);
                TransitionManager.Instance.SetClear();
                NpcTaskQueue.Ins().AssignLanStartingPerform();
            }
            else
            {
                TransitionManager.Instance.Transition(StartSceneType.BloodScabValleyIndoor_3);
                TransitionManager.Instance.SetClear();
                MissBoxPerformManager.Ins().TryInitMissBoxDialogueC();
            }
        }
        #endregion

        #region 分发读档时可能存在的ending
        public bool GetIfCurrNodeIsLeaf()
        {
            return StoryTree.Ins().GetIsLeafById(_gameSession.Data.StoryNodeId);
        }

        public bool Try2AssignEnding()
        {
            Debug.Log("try 2 assign ending on game continue");
            EndingDataInRealSession endingDataG = DataService.Ins().Get<EndingDataInRealSession>();
            if (GetIfCurrNodeIsLeaf())
            {
                string statusName = CFG.Ins().Tables.TbStoryNode.Get(_gameSession.Data.StoryNodeId).StoryNodeStandardName;
                if (!endingDataG.EverComes2ExtraLanEnding && DataService.Ins().Get<NpcTaskDataInRealSession>().EverMetLanInThisRealSession)
                {
                    string[] endingStatus = new string[2];
                    endingStatus[0] = statusName;
                    endingStatus[1] = "LanDetermination";
                    EndingPerformManager.Instance.AssignEndingPerformAndWait(endingStatus, true);
                    endingDataG.EverComes2ExtraLanEnding = true;
                }
                else
                {
                    string[] endingStatus = new string[1];
                    endingStatus[0] = statusName;
                    EndingPerformManager.Instance.AssignEndingPerformAndWait(endingStatus, false);
                }
                return true;
            }
            else if (_gameSession.Data.LeftDrintCnt == 0)
            {
                if (!endingDataG.EverComes2ExtraLanEnding && DataService.Ins().Get<NpcTaskDataInRealSession>().EverMetLanInThisRealSession)
                {
                    string[] endingStatus = new string[2];
                    endingStatus[0] = "Runoutofchance";
                    endingStatus[1] = "LanDetermination";
                    EndingPerformManager.Instance.AssignEndingPerformAndWait(endingStatus, true);
                    endingDataG.EverComes2ExtraLanEnding = true;
                }
                else
                {
                    string[] endingStatus = new string[1];
                    endingStatus[0] = "Runoutofchance";
                    EndingPerformManager.Instance.AssignEndingPerformAndWait(endingStatus, false);
                }
                return true;
            }
            return false;
        }
        #endregion
    }
}